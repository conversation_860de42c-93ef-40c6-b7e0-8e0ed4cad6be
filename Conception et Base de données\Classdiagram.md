# Diagramme de Classes (UML)

Ce diagramme de classes représente la structure logique de l'application de gestion des services techniques, en s'appuyant sur la conception fonctionnelle et l'architecture de la base de données.

```mermaid
classDiagram
    class User {
        +int id
        +string name
        +string email
        +string password
        +Role role
        +Secteur secteur
        +Unite unite
    }
    class Role {
        +int id
        +string name
        +string description
    }
    class Secteur {
        +int id
        +string name
        +string description
        +Unite[] unites
    }
    class Unite {
        +int id
        +string name
        +Secteur secteur
        +string description
        +Employe[] employes
        +Equipement[] equipements
        +Stock[] stocks
        +Installation[] installations
    }
    class Employe {
        +int id
        +string nom
        +string prenom
        +string qualification
        +string formation
        +string certification
        +Affectation[] affectations
    }
    class Affectation {
        +int id
        +Employe employe
        +Unite unite
        +date date_debut
        +date date_fin
    }
    class Equipement {
        +int id
        +string type
        +string marque
        +string modele
        +string numero_serie
        +Unite unite
        +date date_acquisition
        +string etat
        +Intervention[] interventions
    }
    class Vehicule {
        +int id
        +Equipement equipement
        +string type_vehicule
        +int kilometrage
    }
    class Char {
        +int id
        +Equipement equipement
        +string type_char
    }
    class SousEnsemble {
        +int id
        +Equipement equipement
        +string type
        +string qr_code
    }
    class Stock {
        +int id
        +Unite unite
        +string type_stock
        +int seuil_alerte
        +PieceRechange[] pieces_rechange
        +ProduitEntretien[] produits_entretien
        +HuileCarburant[] huiles_carburants
        +Outillage[] outillages
    }
    class PieceRechange {
        +int id
        +Stock stock
        +string categorie
        +string nom
        +int quantite
        +int seuil_critique
    }
    class ProduitEntretien {
        +int id
        +Stock stock
        +string nom
        +int quantite
    }
    class HuileCarburant {
        +int id
        +Stock stock
        +string type
        +int quantite
    }
    class Outillage {
        +int id
        +Stock stock
        +string nom
        +string etat
        +string utilisation
    }
    class Intervention {
        +int id
        +Equipement equipement
        +string type
        +date date
        +string description
        +Employe employe
        +string statut
        +HistoriqueIntervention[] historique
    }
    class HistoriqueIntervention {
        +int id
        +Intervention intervention
        +date date
        +string action
        +string commentaire
    }
    class Installation {
        +int id
        +string type
        +string localisation
        +string etat
        +Unite unite
    }
    class Notification {
        +int id
        +User user
        +string message
        +string type
        +date date
        +bool lu
    }
    class Message {
        +int id
        +User sender
        +User receiver
        +string content
        +date date
    }
    class Rapport {
        +int id
        +string type
        +Unite unite
        +Secteur secteur
        +date date
        +string fichier_pdf
    }
    class KPI {
        +int id
        +Unite unite
        +Secteur secteur
        +string type
        +float valeur
        +date date
    }

    %% Relations
    Secteur "1" -- "*" Unite : contient
    Unite "1" -- "*" Employe : emploie
    Unite "1" -- "*" Equipement : possede
    Unite "1" -- "*" Stock : gere
    Unite "1" -- "*" Installation : possede
    Employe "1" -- "*" Affectation : a
    Equipement "1" -- "*" Intervention : subit
    Equipement "1" -- "*" Vehicule : est
    Equipement "1" -- "*" Char : est
    Equipement "1" -- "*" SousEnsemble : contient
    Stock "1" -- "*" PieceRechange : contient
    Stock "1" -- "*" ProduitEntretien : contient
    Stock "1" -- "*" HuileCarburant : contient
    Stock "1" -- "*" Outillage : contient
    Intervention "1" -- "*" HistoriqueIntervention : documente
    User "1" -- "*" Notification : recoit
    User "1" -- "*" Message : echange
    Unite "1" -- "*" Rapport : produit
    Unite "1" -- "*" KPI : mesure
```

---

Ce diagramme peut être enrichi ou adapté selon l'évolution des besoins métier et techniques. Il offre une vue d'ensemble professionnelle et modulaire du système.
