// Prisma schema for the Technical Services Management Application
// Generated from Conception.md, ArchitectureDB.md, and Classdiagram.md

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Role {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  users       User[]
}

model Secteur {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  unites      Unite[]
  rapports    Rapport[]
  kpis        KPI[]
}

model Unite {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  secteur     Secteur  @relation(fields: [secteurId], references: [id])
  secteurId   Int
  employes    Employe[]
  equipements Equipement[]
  stocks      Stock[]
  installations Installation[]
  rapports    Rapport[]
  kpis        KPI[]
  users       User[]
}

model User {
  id        Int      @id @default(autoincrement())
  name      String
  email     String   @unique
  password  String
  role      Role     @relation(fields: [roleId], references: [id])
  roleId    Int
  secteur   Secteur? @relation(fields: [secteurId], references: [id])
  secteurId Int?
  unite     Unite?   @relation(fields: [uniteId], references: [id])
  uniteId   Int?
  notifications Notification[]
  sentMessages     Message[]   @relation("SentMessages")
  receivedMessages Message[]   @relation("ReceivedMessages")
}

model Employe {
  id            Int           @id @default(autoincrement())
  nom           String
  prenom        String
  qualification String?
  formation     String?
  certification String?
  unite         Unite         @relation(fields: [uniteId], references: [id])
  uniteId       Int
  affectations  Affectation[]
  interventions Intervention[]
}

model Affectation {
  id        Int      @id @default(autoincrement())
  employe   Employe  @relation(fields: [employeId], references: [id])
  employeId Int
  unite     Unite    @relation(fields: [uniteId], references: [id])
  uniteId   Int
  date_debut DateTime
  date_fin   DateTime?
}

model Equipement {
  id              Int           @id @default(autoincrement())
  type            String
  marque          String?
  modele          String?
  numero_serie    String?
  unite           Unite         @relation(fields: [uniteId], references: [id])
  uniteId         Int
  date_acquisition DateTime?
  etat            String?
  interventions   Intervention[]
  vehicule        Vehicule?
  char            Char?
  sousEnsembles   SousEnsemble[]
}

model Vehicule {
  id            Int        @id @default(autoincrement())
  equipement    Equipement @relation(fields: [equipementId], references: [id])
  equipementId  Int        @unique
  type_vehicule String?
  kilometrage   Int?
}

model Char {
  id           Int        @id @default(autoincrement())
  equipement   Equipement @relation(fields: [equipementId], references: [id])
  equipementId Int        @unique
  type_char    String?
}

model SousEnsemble {
  id           Int        @id @default(autoincrement())
  equipement   Equipement @relation(fields: [equipementId], references: [id])
  equipementId Int
  type         String?
  qr_code      String?
}

model Stock {
  id               Int                @id @default(autoincrement())
  unite            Unite              @relation(fields: [uniteId], references: [id])
  uniteId          Int
  type_stock       String?
  seuil_alerte     Int?
  piecesRechange   PieceRechange[]
  produitsEntretien ProduitEntretien[]
  huilesCarburants HuileCarburant[]
  outillages       Outillage[]
}

model PieceRechange {
  id            Int    @id @default(autoincrement())
  stock         Stock  @relation(fields: [stockId], references: [id])
  stockId       Int
  categorie     String?
  nom           String
  quantite      Int
  seuil_critique Int?
}

model ProduitEntretien {
  id        Int    @id @default(autoincrement())
  stock     Stock  @relation(fields: [stockId], references: [id])
  stockId   Int
  nom       String
  quantite  Int
}

model HuileCarburant {
  id        Int    @id @default(autoincrement())
  stock     Stock  @relation(fields: [stockId], references: [id])
  stockId   Int
  type      String?
  quantite  Int
}

model Outillage {
  id         Int    @id @default(autoincrement())
  stock      Stock  @relation(fields: [stockId], references: [id])
  stockId    Int
  nom        String
  etat       String?
  utilisation String?
}

model Intervention {
  id            Int      @id @default(autoincrement())
  equipement    Equipement @relation(fields: [equipementId], references: [id])
  equipementId  Int
  type          String?
  date          DateTime
  description   String?
  employe       Employe   @relation(fields: [employeId], references: [id])
  employeId     Int
  statut        String?
  historique    HistoriqueIntervention[]
}

model HistoriqueIntervention {
  id             Int           @id @default(autoincrement())
  intervention   Intervention  @relation(fields: [interventionId], references: [id])
  interventionId Int
  date           DateTime
  action         String?
  commentaire    String?
}

model Installation {
  id           Int    @id @default(autoincrement())
  type         String?
  localisation String?
  etat         String?
  unite        Unite  @relation(fields: [uniteId], references: [id])
  uniteId      Int
}

model Notification {
  id        Int      @id @default(autoincrement())
  user      User     @relation(fields: [userId], references: [id])
  userId    Int
  message   String
  type      String?
  date      DateTime
  lu        Boolean @default(false)
}

model Message {
  id        Int      @id @default(autoincrement())
  sender    User     @relation("SentMessages", fields: [senderId], references: [id])
  senderId  Int
  receiver  User     @relation("ReceivedMessages", fields: [receiverId], references: [id])
  receiverId Int
  content   String
  date      DateTime
}

model Rapport {
  id         Int      @id @default(autoincrement())
  type       String?
  unite      Unite?   @relation(fields: [uniteId], references: [id])
  uniteId    Int?
  secteur    Secteur? @relation(fields: [secteurId], references: [id])
  secteurId  Int?
  date       DateTime
  fichier_pdf String?
}

model KPI {
  id        Int      @id @default(autoincrement())
  unite     Unite?   @relation(fields: [uniteId], references: [id])
  uniteId   Int?
  secteur   Secteur? @relation(fields: [secteurId], references: [id])
  secteurId Int?
  type      String?
  valeur    Float?
  date      DateTime
} 