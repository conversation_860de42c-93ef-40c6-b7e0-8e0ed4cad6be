# Architecture de la Base de Données

## Tables Principales

### 1. Utilisateurs et Rôles
- **users** (id, name, email, password, role_id, secteur_id, unite_id, ...)
- **roles** (id, name, description)
- **secteurs** (id, name, description)
- **unites** (id, name, secteur_id, description)

### 2. Ressources Humaines
- **employes** (id, nom, prenom, qualification, formation, certification, affectation_id, ...)
- **affectations** (id, employe_id, unite_id, date_debut, date_fin)

### 3. Équipements
- **equipements** (id, type, marque, modele, numero_serie, unite_id, date_acquisition, etat, ...)
- **vehicules** (id, equipement_id, type_vehicule, kilometrage, ...)
- **chars** (id, equipement_id, type_char, ...)
- **sous_ensembles** (id, equipement_id, type, qr_code, ...)

### 4. Stocks
- **stocks** (id, unite_id, type_stock, seuil_alerte, ...)
- **pieces_rechange** (id, stock_id, categorie, nom, quantite, seuil_critique, ...)
- **produits_entretien** (id, stock_id, nom, quantite, ...)
- **huiles_carburants** (id, stock_id, type, quantite, ...)
- **outillages** (id, stock_id, nom, etat, utilisation, ...)

### 5. Interventions
- **interventions** (id, equipement_id, type, date, description, employe_id, statut, ...)
- **historique_interventions** (id, intervention_id, date, action, commentaire, ...)

### 6. Installations
- **installations** (id, type, localisation, etat, unite_id, ...)

### 7. Notifications et Communication
- **notifications** (id, user_id, message, type, date, lu)
- **messages** (id, sender_id, receiver_id, content, date, ...)

### 8. Rapports et Visualisation
- **rapports** (id, type, unite_id, secteur_id, date, fichier_pdf, ...)
- **kpis** (id, unite_id, secteur_id, type, valeur, date, ...)

---

## Relations Clés (ERD simplifié)

- Un **secteur** a plusieurs **unités**
- Une **unité** a plusieurs **employés**, **équipements**, **stocks**, **installations**
- Un **employé** peut avoir plusieurs **affectations**
- Un **équipement** peut être un **véhicule**, un **char** ou un **sous-ensemble**
- Un **équipement** a plusieurs **interventions**
- Un **stock** contient plusieurs **pièces de rechange**, **produits d'entretien**, **huiles/carburants**, **outillages**
- Une **intervention** est réalisée par un **employé** sur un **équipement**
- Les **notifications** sont adressées aux **utilisateurs**
- Les **rapports** et **KPIs** sont liés aux **unités** et **secteurs**

---

## Exemple de Diagramme (Markdown)

```mermaid
erDiagram
  SECTEURS ||--o{ UNITES : contient
  UNITES ||--o{ EMPLOYES : emploie
  UNITES ||--o{ EQUIPEMENTS : possede
  UNITES ||--o{ INSTALLATIONS : possede
  UNITES ||--o{ STOCKS : gere
  EMPLOYES ||--o{ AFFECTATIONS : a
  EQUIPEMENTS ||--o{ INTERVENTIONS : subit
  EQUIPEMENTS ||--|{ VEHICULES : est
  EQUIPEMENTS ||--|{ CHARS : est
  EQUIPEMENTS ||--|{ SOUS_ENSEMBLES : contient
  STOCKS ||--o{ PIECES_RECHANGE : contient
  STOCKS ||--o{ PRODUITS_ENTRETIEN : contient
  STOCKS ||--o{ HUILES_CARBURANTS : contient
  STOCKS ||--o{ OUTILLAGES : contient
  INTERVENTIONS ||--o{ HISTORIQUE_INTERVENTIONS : documente
  USERS ||--o{ NOTIFICATIONS : recoit
  USERS ||--o{ MESSAGES : echange
  UNITES ||--o{ RAPPORTS : produit
  UNITES ||--o{ KPIS : mesure
```

---

Ce schéma peut être adapté selon les besoins spécifiques et les détails supplémentaires du projet.
